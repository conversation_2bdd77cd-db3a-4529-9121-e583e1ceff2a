import request from '@/utils/request/uni-request'

const base = '/public/auth'
/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 陪诊师登录
   * @param {Object} loginData - 登录数据
   * @param {string} loginData.phone - 手机号
   * @param {string} loginData.loginType - 登录类型：password-密码登录，sms-短信验证码登录
   * @param {string} loginData.password - 密码（密码登录时必填）
   * @param {string} loginData.smsCode - 短信验证码（短信登录时必填）
   * @param {string} loginData.deviceInfo - 设备信息
   * @returns {Promise} 登录响应
   */
  login(loginData) {
    return request.post(`${base}/login`, loginData, {
      isNeedToken: false
    })
  },

  /**
   * 刷新token
   * @param {string} refreshToken - 刷新令牌
   * @returns {Promise} 刷新响应
   */
  refreshToken(refreshToken) {
    return request.post(`${base}/token/refresh`, {
      refreshToken
    }, {
      isNeedToken: false
    })
  },
  /**
   * 发送短信验证码
   * @param {Object} smsData - 短信数据
   * @param {string} smsData.phone - 手机号
   * @param {string} smsData.codeType - 验证码类型：login-登录验证码，register-注册验证码，reset-重置密码验证码
   * @param {string} smsData.templateId - 短信模板ID（可选）
   * @param {Object} smsData.params - 短信模板参数（可选）
   * @returns {Promise} 发送响应
   */
  sendSmsCode(smsData) {
    console.log('smsData', smsData);

    return request.post(`${base}/sms/send`, smsData, {
      isNeedToken: false
    })
  },

  /**
   * 验证短信验证码
   * @param {string} phone - 手机号
   * @param {string} code - 验证码
   * @param {string} codeType - 验证码类型
   * @returns {Promise} 验证响应
   */
  verifySmsCode(phone, code, codeType) {
    return request.post(`${base}/sms/verify`, null, {
      params: {
        arg0: phone,
        arg1: code,
        arg2: codeType
      },
      isNeedToken: false
    })
  },

  /**
   * 重置密码
   * @param {number} userId - 用户ID
   * @param {string} newPassword - 新密码
   * @returns {Promise} 重置响应
   */
  resetPassword(userId, newPassword) {
    return request.post(`/accompany/platform/user/${userId}/reset-password`, null, {
      params: {
        arg1: newPassword
      }
    })
  },

  /**
   * 获取陪诊师详情
   * @param {number} userId - 用户ID
   * @returns {Promise} 用户详情
   */
  getUserDetail(userId) {
    return request.get(`/platform/user/${userId}`)
  },

  /**
   * 编辑陪诊师信息
   * @param {Object} editData - 编辑数据
   * @returns {Promise} 编辑响应
   */
  editUser(editData) {
    return request.put('/platform/user/edit', editData)
  },


}

export default authApi
