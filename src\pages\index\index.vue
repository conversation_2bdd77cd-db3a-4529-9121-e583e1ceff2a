<template>
  <view class="home-page">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <view class="user-avatar">
          <image class="avatar-img" src="../../static/logo.png" mode="aspectFill"></image>
        </view>
        <view class="user-details">
          <view class="user-name">王晓</view>
          <view class="user-job">工号：10086</view>
        </view>
        <view class="user-arrow">
          <image class="arrow-icon" src="@/static/public/chevron-right.png" mode="aspectFit"></image>
        </view>
      </view>
    </view>

    <!-- 标签导航 -->
    <view class="tab-navigation">
      <view
        v-for="(item, index) in tagList"
        :key="index"
        class="tab-item"
        :class="{ 'tab-active': item.current }"
        @click="handleTagClick(index)"
      >
        <text class="tab-title">{{ item.title }}</text>
        <view v-if="item.num > 0" class="tab-badge">{{ item.num }}</view>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list">
      <view v-for="(item, index) in taskList" :key="index" class="order-item">
        <view class="order-header">
          <view class="order-title">半天陪诊（四小时）</view>
          <view class="order-status" :class="getStatusClass(item.status)">
            {{ getDictLabel('ORDER_STATUS', item.status || 0) }}
          </view>
        </view>
        <view class="br-view"></view>
        <view class="order-details">
          <view class="detail-row">
            <view class="detail-icon">
              <image class="icon-img" src="@/static/public/calendar.png" mode="aspectFit"></image>
            </view>
            <text class="detail-text">2025-06-10上午</text>
          </view>
          <view class="detail-row">
            <view class="detail-icon">
              <image class="icon-img" src="@/static/public/location.png" mode="aspectFit"></image>
            </view>
            <text class="detail-text">云南省第一人民医院</text>
          </view>
          <view class="detail-row">
            <view class="detail-icon">
              <image class="icon-img" src="@/static/public/location.png" mode="aspectFit"></image>
            </view>
            <text class="detail-text">云南省第一人民医院</text>
          </view>
          <view class="detail-row">
            <view class="detail-icon">
              <image class="icon-img" src="@/static/public/user.png" mode="aspectFit"></image>
            </view>
            <text class="detail-text">王建国 男 13812341234</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { useRouter } from "uniapp-router-next";
import DictManager from "@/utils/dict"
import { ref } from "vue";

const router = useRouter();

const tagList = ref([
  {
    title: '今日',
    num: 0,
    current: true
  },
  {
    title: '遗留',
    num: 0,
    current: false
  },
  {
    title: '待开始',
    num: 5,
    current: false
  },
  {
    title: '全部订单',
    num: 0,
    current: false
  }
])

const taskList = ref([
  {
    status: 'in_progress'
  },
  {
    status: 'pending'
  },
  {
    status: 'completed'
  }
])

const handleTagClick = (index) => {
  tagList.value.forEach((item, i) => {
    item.current = i === index
  })
}

const getDictLabel = (dictName, value) => {
  // 这里可以根据dictName来选择不同的字典
  const statusDict = {
    'in_progress': '进行中',
    'pending': '待开始',
    'completed': '已完成'
  }
  return statusDict[value] || '未知状态'
}

const getStatusClass = (status) => {
  const statusClasses = {
    'in_progress': 'status-progress',
    'pending': 'status-pending',
    'completed': 'status-completed'
  }
  return statusClasses[status] || 'status-default'
}
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 状态栏占位
.status-bar {
  height: var(--status-bar-height, 44px);
  background-color: #fff;
}


// 用户信息卡片
.user-card {
  padding: 32rpx;
  background-color: #fff;

  .user-info {
    display: flex;
    align-items: center;

    .user-avatar {
      width: 96rpx;
      height: 96rpx;
      margin-right: 24rpx;

      .avatar-img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: #f0f0f0;
      }
    }

    .user-details {
      flex: 1;

      .user-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }

      .user-job {
        font-size: 28rpx;
        color: #666;
      }
    }

    .user-arrow {
      width: 48rpx;
      height: 48rpx;

      .arrow-icon {
        width: 100%;
        height: 100%;
        opacity: 0.6;
      }
    }
  }
}

// 标签导航
.tab-navigation {
  display: flex;
  background-color: #fff;
  padding: 8rpx 8rpx 16rpx 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  gap:16rpx;
  .tab-item {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 64rpx;
    border-radius: 1998rpx;
    transition: all 0.3s ease;
    background-color: $uni-bg-color-grey;

    .tab-title {
      font-size: 28rpx;
      color: #666;
      transition: color 0.3s ease;
    }

    .tab-badge {
      position: absolute;
      top: 2rpx;
      right: 16rpx;
      width: 32rpx;
      height: 32rpx;
      background-color: $uni-color-error;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20rpx;
      font-weight: 600;
      color: #fff;
      transform: scale(0.8);
    }

    &.tab-active {
      background-color: $uni-color-second-primary;

      .tab-title {
        color:$uni-color-primary;
        font-weight: 600;
      }
    }
  }
}

// 订单列表
.order-list {
  padding: 18rpx 22rpx;

  .order-item {
    background-color: #fff;
    border-radius: 18rpx;
    padding: 20rpx;
    margin-bottom: 12rpx;

    .order-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12rpx;

      .order-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        flex: 1;
      }

      .order-status {
        padding: 8rpx 16rpx;
        border-radius: 6rpx;
        font-size: 24rpx;
        font-weight: 500;

        &.status-progress {
          background-color: #e8f5e8;
          color: #52c41a;
        }

        &.status-pending {
          background-color: #fff7e6;
          color: #fa8c16;
        }

        &.status-completed {
          background-color: #f0f0f0;
          color: #666;
        }

        &.status-default {
          background-color: #f0f0f0;
          color: #999;
        }
      }
    }
    .br-view{
      background-color: $uni-br-color;
      height: 1rpx;
    }
    .order-details {
      margin-top: 20rpx;
      display: flex;
      flex-direction: column;
      gap:18rpx;
      .detail-row {
        display: flex;
        align-items: center;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 16rpx;

          .icon-img {
            width: 100%;
            height: 100%;
            opacity: 0.6;
          }
        }

        .detail-text {
          font-size: 28rpx;
          color: $uni-text-color-black;
          flex: 1;
        }
      }
    }
  }
}

// 底部安全区域
.safe-area-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}
</style>
