<template>
  <view class="content">
    <view class="page-top">
      <view>
        <view>
          <view>
            <img src="../../static/logo.png" alt="">
          </view>
        </view>
        <view>
          <view>李明</view>
          <view>工号：10086</view>
        </view>
        <view>
          <img src="@/static/public/chevron-right.png" alt="">
        </view>
      </view>
      <view>
        <view v-for="(item, index) in tagList" :key="index" :class="{ 'active': item.current }" @click="handleTagClick(index)">
          {{ item.title }}>
          <view>{{ item.num }}</view>
        </view>
      </view>
    </view>
    <view>
      <uni-list>
        <uni-list-item v-for="(item, index) in taskList" :key="index">
          <view>
            <view>
              <view>半天陪诊（四小时）</view>
              <view>{{getDictLabel('ORDER_STATUS',item.status||0)}}</view>
            </view>
            <view>
              <view>
                <view>
                  <img src="@/static/public/" alt="">
                </view>
                <view></view>
              </view>
            </view>
          </view>
        </uni-list-item>
      </uni-list>
    </view>
  </view>
</template>

<script setup>
import { useRouter } from "uniapp-router-next";
import DictManager, {ORDER_STATUS} from "@/utils/dict"
import { ref } from "vue";
const router = useRouter();
const tagList = [
  {
    title:'今日',
    num:0,
    current:true
  },
  {
    title:'遗留',
    num:0,
    current:true
  },
  {
    title:'待开始',
    num:0,
    current:true
  },
  {
    title:'全部订单',
    num:0,
    current:true
  }
]
const taskList = ref([
  {
    
  }
])
const handleTagClick = () => {

}
const getDictLabel = (dictName,value) => {
  return DictManager.getLabel(dictName,value)
}
</script>

<style>

</style>
