<template>
  <view class="login-page">
    <image class="logo" src="../../static/logo.png" />
    <view class="logo-text">陪诊师端</view>
    <text v-if="loginForm.loginType == 1" class="login-type-tip">账号密码登录</text>
    <text v-if="loginForm.loginType == 2" class="login-type-tip">手机验证码登录</text>
    <view v-if="loginForm.loginType == 2" class="login-form">
      <view class="login-form__item">
        <uni-easyinput v-model="loginForm.username" :clearable="false" placeholder="请输入手机号码" />
      </view>
      <view class="login-form__item">
        <uni-easyinput
          :value="loginForm.credentials"
          :clearable="false"
          placeholder="请输入手机验证码"
          :maxlength="6"
          @input="bindCodeChange"
        >
          <template #right>
            <button
              plain
              size="mini"
              style="border: 0px"
              :disabled="telCodeDuration > 0"
              @click="getCode"
              @change="bindCredentialsCode"
           >
              {{ telCodeDuration > 0 ? '重新获取' + telCodeDuration + 's' : '获取验证码' }}
            </button>
          </template>
        </uni-easyinput>
      </view>
    </view>
    <view v-if="loginForm.loginType == 1" class="login-form">
      <view class="login-form__item">
        <uni-easyinput v-model="loginForm.username" :clearable="false" placeholder="请输入账号名" />
      </view>
      <view class="login-form__item">
        <uni-easyinput
          v-model="loginForm.credentials"
          ref="pwdInputRef"
          :clearable="false"
          :passwordIcon="false"
          type="password"
          placeholder="请输入密码"
        >
           <template #right>
            <view class="icon-visible" @click="changePwdIsVisible">
              <image class="icon-img" v-if="pwdIsVisible" src="/static/img/login/visible.png" alt="" />
              <image class="icon-img" v-else src="/static/img/login/invisible.png" alt="" />
            </view>
           </template>
        </uni-easyinput>
      </view>
    </view>
    <view class="tooltip">
      <text @click="toResetPassword">忘记密码</text>
      <text v-if="loginForm.loginType == 2" @click="switchLoginType(1)">使用密码登录</text>
      <text v-if="loginForm.loginType == 1" @click="switchLoginType(2)">使用验证码登录</text>
    </view>
    <button class="login-btn" :disabled="!allowLogin" @click="login">登录</button>
    <checkbox-group class="agree-label" @change="changeAgree">
      <label>
        <checkbox class="agree-checkbox" :checked="agreeCheck[0]" color="#1bb2b2" />
        我已阅读并同意
        <text class="service-tip" @click.stop="toAgreement">《服务协议》</text>
        和
        <text class="service-tip" @click.stop="toPrivacy">《隐私政策》</text>
      </label>
    </checkbox-group>
    <view class="content-bottom">
      <view class="bottom-toolbar">
        <!-- <view @click="toRegister">医师注册</view> -->
        <view @click="toService">联系客服</view>
      </view>
      <view class="bottom-tip">如果您是患者，请在微信内搜索公众号"滇医通官方号"使用挂号等服务</view>
      <view v-if="appVersion" class="bottom-tip">当前版本: {{ appVersion }}</view>
    </view>
  </view>
</template>

<script setup>
  import debounce from 'lodash.debounce';
  import { authApi } from '@/apis/auth';
  import { useAppStore } from '@/store/modules/app';
  import { useUserStore } from '@/store/modules/user';
  import { useRouter } from "uniapp-router-next";
  import { onShow } from '@dcloudio/uni-app';
  import { computed, reactive, ref } from 'vue';
  import dayjs from 'dayjs';

  const { appVersion } = useAppStore();
  const userStore = useUserStore();
  const router = useRouter();
  const telCodeExpireDate = ref(dayjs(new Date()));
  const telCodeDuration = ref(0);
  const loginForm = reactive({
    username: '',
    credentials: '',
    loginType: 2,
    clientId: 'MP_MINIAPP',
    miniprogramCode: null,
  });
  const agreeCheck = ref([false]);
  const pwdIsVisible = ref(true);
  const pwdInputRef = ref()

  const bindCredentialsCode = debounce(function (e) {
    loginForm.credentials = e;
  }, 500);

  const allowLogin = computed(() => {
    return loginForm.username != '' && loginForm.credentials != '' && agreeCheck.value[0];
  });
  function bindCodeChange(val) {
    loginForm.credentials = val?.length > 6 ? val.substring(0, 6) : val;
  }
  function changePwdIsVisible() {
    pwdIsVisible.value = !pwdIsVisible.value;
    // console.log('pwdInputRef',pwdInputRef.value);
    pwdInputRef.value.onEyes()
  }
  function getCode(e) {
    if (!loginForm.username) {
      if (loginForm.loginType == 2) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        });
      } else {
        uni.showToast({
          title: '请输入账号',
          icon: 'none'
        });
      }
      return;
    }
    uni.showLoading({
      title: '发送中...'
    });
    console.log('loginForm', loginForm);
    
    authApi.sendSmsCode({
      phone: loginForm.username,
      codeType: 'login',
      templateId: '',
      params: {}
    })
      .then(() => {
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        });
        telCodeDuration.value = 60;
        telCodeExpireDate.value = dayjs().add(60, 'second');
        const timer = setInterval(() => {
          if (telCodeDuration.value > 0) {
            telCodeDuration.value = telCodeExpireDate.value.diff(dayjs(new Date()), 's');
          } else {
            telCodeDuration.value = 0;
            clearInterval(timer);
          }
        }, 1000);
      })
      .catch((error) => {
        uni.showToast({
          title: error.message || '发送失败',
          icon: 'none'
        });
      })
      .finally(() => {
        uni.hideLoading();
      });
  }
  function switchLoginType(value) {
    loginForm.username = '';
    loginForm.credentials = '';
    loginForm.loginType = value;
  }
  function changeAgree() {
    agreeCheck.value = [!agreeCheck.value[0]];
  }
  function toRegister() {
    router.navigateTo({
      url: '/pages/public/register-terms'
    });
  }
  function toPrivacy() {
    router.navigateTo({
      url: '/pages/public/privacy'
    });
  }
  function toAgreement() {
    router.navigateTo({
      url: '/pages/public/agreement'
    });
  }
  function toService() {
    uni.makePhoneCall({
      phoneNumber: '4006639993', //电话号码
      success: function (e) {},
      fail: function (e) {},
    })
  }
  function toResetPassword() {
    router.navigateTo({
      url: '/pages/public/reset-password'
    });
  }
  async function login() {
    const loginData = { ...loginForm };
    if (!loginData.username) {
      if (loginData.loginType == 2) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        });
      } else {
        uni.showToast({
          title: '请输入账号',
          icon: 'none'
        });
      }
      return;
    }
    if (!loginData.credentials) {
      if (loginData.loginType == 2) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        });
      } else {
        uni.showToast({
          title: '请输入密码',
          icon: 'none'
        });
      }
      return;
    }
    if (!agreeCheck.value[0]) {
      uni.showToast({
        title: '请阅读并同意《服务协议》和《隐私政策》',
        icon: 'none'
      });
      return;
    }
    try {
      const { code: miniprogramCode } = await uni.login({ onlyAuthorize: true });
      loginData.miniprogramCode = miniprogramCode;
    } catch (e) {
      /* empty */
    }

    uni.showLoading({
      title: '登录中...'
    });

    try {
      // 构建登录参数
      const loginParams = {
        phone: loginData.username,
        loginType: loginData.loginType === 1 ? 'password' : 'sms',
        password: loginData.loginType === 1 ? loginData.credentials : undefined,
        smsCode: loginData.loginType === 2 ? loginData.credentials : undefined,
        deviceInfo: JSON.stringify({
          platform: 'MP_MINIAPP',
          code: loginData.miniprogramCode
        })
      };
      const response = await authApi.login(loginParams);
      console.log('response',response);

      await userStore.login(response);


      uni.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 登录成功后跳转
      setTimeout(() => {
        router.reLaunch({
          url: '/pages/index/index'
        });
      }, 1500);

    } catch (error) {
      console.error('Login error:', error);
      uni.showToast({
        title: error.message || '登录失败，请重试',
        icon: 'none',
        duration: 2000
      });
    } finally {
      uni.hideLoading();
    }
  }
  onShow(() => {
    // 清除之前的登录状态
    userStore.clearTokens();
    uni.hideHomeButton();
    loginForm.clientId = 'MP_MINIAPP';
  });
</script>

<style lang="scss" scoped>
  .login-page {
    padding: 0 32rpx;
    padding-top: 120rpx;
    box-sizing: border-box;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .content-bottom {
    margin-top: auto;
    margin-bottom: $uni-spacing-row-base;
  }

  .logo {
    width: 200rpx;
    height: 200rpx;
    margin-top: 64rpx;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 8rpx;
  }
  .logo-text {
    border-radius: 200rpx;
    border: 2rpx solid $uni-primary-2;
    color: $uni-primary-2;
    font-size: 24rpx;
    line-height: 40rpx;
    text-align: center;
    padding: 0 28rpx;
    width: max-content;
    margin: 0 auto 24rpx;
  }

  .login-form {
    width: 100%;
    margin-top: $uni-spacing-col-lg;
    display: flex;
    flex-direction: column;
    justify-content: center;
    &__title {
      font-weight: bold;
    }
    &__item {
      margin-bottom: 64rpx;
      :deep(.uni-easyinput__content) {
        padding: 12rpx 20rpx !important;
        border-radius: 50rpx !important;
        border: 2rpx solid #DAE2E5!important;
      }
      :deep(.uni-easyinput__placeholder-class) {
        color: #ADBCBF!important;
        font-size: 36rpx !important;
      }
      :deep(.uni-easyinput__content-input){
        font-size: 36rpx !important;
        // line-height: 98rpx !important;
      }
      :deep(button){
        color: #1bb2b2;
        font-size: 0.875rem;
        font-weight: 600;
      }
      :deep(.icon-visible){
        width: 48rpx;
        height: 48rpx;
        .icon-img{
          width: 100%;
          height: 100%;
        }
      }
    }
    &__getCode {
      opacity: 0.6;
      margin-right: $uni-spacing-row-base;
    }
  }
  .tooltip {
    display: flex;
    justify-content: space-between;
    text {
      color: #727D7F;
    }
  }
  
  .login-btn {
    width: 70%;
    margin-top: 80rpx;
    border-radius: 50rpx;
    font-size: 40rpx;
    font-weight: 600;
  }
  button[disabled]:not([type]).login-btn{
    color: $uni-text-color-inverse !important;
    background-color: #adbcbf !important;
  }
  button.login-btn{
    color: $uni-text-color-inverse !important;
    background-color: $uni-color-primary !important;
  }
  // button[disabled]:not([type]) {
  //     color: $uni-text-color-inverse !important;
  //     background-color: #adbcbf !important;
  //   }
  .agree-label {
    margin-top: $uni-spacing-col-lg;
    font-size: 28rpx;
    display: flex;
    justify-content: center;

    .agree-checkbox {
      transform: scale(0.7);
    }
  }
  .bottom-toolbar {
    display: flex;
    justify-content: space-between;
    margin: 0 160rpx 40rpx 160rpx;
    color: $uni-color-primary;
  }
  .bottom-tip {
    text-align: center;
    color: $uni-text-color-grey;
    font-size: 26rpx;
    margin-bottom: $uni-spacing-col-base;
  }
  .login-tip {
    font-size: 32rpx;
    color: $uni-text-color-grey;
    margin-bottom: 40rpx;
  }
  .login-type-tip {
    font-size: 40rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
  }
  .service-tip {
    color: $uni-color-primary;
  }
</style>
