<template>
  <view class="rebind-tel-page">
    <text class="rebind-tel-page__title">输入要重置密码的手机号码</text>
    <view class="input-container">
      <uni-easyinput
        v-model="loginForm.tel"
        :input-border="false"
        :clearable="false"
        placeholder="请输入手机号码"
      ></uni-easyinput>
    </view>
    <view class="input-container">
      <uni-easyinput
        :value="loginForm.verifyCode"
        :input-border="false"
        :clearable="false"
        placeholder="请输入验证码"
        :maxlength="6"
        @input="bindCodeChange"
      >
        <template #right>
          <button plain size="mini" style="border: 0px" :disabled="telCodeDuration > 0" @click="getCode">
            {{ telCodeDuration > 0 ? '重新获取' + telCodeDuration + 's' : '获取验证码' }}
          </button>
        </template>
      </uni-easyinput>
    </view>
    <view class="input-container">
      <uni-easyinput
        v-model="loginForm.newPassword"
        :input-border="false"
        :clearable="false"
        type="password"
        placeholder="请输入新密码"
      ></uni-easyinput>
    </view>
    <button type="primary" class="confirm-btn" @click="resetPassword">提交</button>
  </view>
</template>
<script setup lang="ts">
  import DoctorInfoApi from '@/apis/doctor/doctor-info-api';
  import AppGlobal from '@/config/AppGlobal';
  import AppToast from '@/utils/AppToast';
  import { reactive, ref } from 'vue';
  import dayjs from 'dayjs';
  import AuthApi from '@/apis/public/auth-api';

  const telCodeExpireDate = ref(dayjs(new Date()));
  const telCodeDuration = ref(0);
  const loginForm = reactive({
    tel: '',
    verifyCode: '',
    newPassword: ''
  });
  function bindCodeChange(val: string) {
    loginForm.verifyCode = val?.length > 6 ? val.substring(0, 6) : val;
  }
  function getCode(e) {
    if (!loginForm.tel) {
      AppToast.error('请输入手机号');
      return;
    }
    AppToast.loading();
    AuthApi.getResetTelCode(loginForm.tel)
      .then(() => {
        AppToast.success('验证码已发送');
        telCodeDuration.value = 60;
        telCodeExpireDate.value = dayjs().add(60, 'second');
        const timer = setInterval(() => {
          if (telCodeDuration.value > 0) {
            telCodeDuration.value = telCodeExpireDate.value.diff(dayjs(new Date()), 's');
          } else {
            clearInterval(timer);
          }
        }, 1000);
      })
      .finally(() => {
        AppToast.close();
      });
  }

  function resetPassword() {
    if (!loginForm.tel) {
      AppToast.error('请输入手机号');
      return;
    }
    if (!loginForm.verifyCode) {
      AppToast.error('请输入验证码');
      return;
    }
    AppToast.loading();
    AuthApi.resetPassword({ ...loginForm })
      .then(() => {
        AppToast.showModal('提示', '密码重置成功，请您重新登录', { showCancel: false }).then(() => {
          AppGlobal.CurrentUser.logout();
        });
      })
      .finally(() => {
        AppToast.close();
      });
  }
</script>
<style lang="scss" scoped>
  .rebind-tel-page {
    padding: $uni-spacing-col-lg $uni-spacing-row-lg;
    &__title {
      font-size: 16px;
      font-weight: bold;
      padding-left: 10px;
    }
    .input-container {
      border-bottom: 1px solid whitesmoke;
      margin-top: $uni-spacing-col-lg;
    }
    .confirm-btn {
      margin-top: $uni-spacing-col-lg * 3;
      border-radius: 64px;
    }
  }
</style>
