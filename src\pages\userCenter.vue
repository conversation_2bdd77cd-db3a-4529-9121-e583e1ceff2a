<template>
  <view class="rebind-tel-page">
    <text class="rebind-tel-page__title">输入要重置密码的手机号码</text>
    <view class="input-container">
      <uni-easyinput
        v-model="loginForm.tel"
        :input-border="false"
        :clearable="false"
        placeholder="请输入手机号码"
      ></uni-easyinput>
    </view>
    <view class="input-container">
      <uni-easyinput
        :value="loginForm.verifyCode"
        :input-border="false"
        :clearable="false"
        placeholder="请输入验证码"
        :maxlength="6"
        @input="bindCodeChange"
      >
        <template #right>
          <button plain size="mini" style="border: 0px" :disabled="telCodeDuration > 0" @click="getCode">
            {{ telCodeDuration > 0 ? '重新获取' + telCodeDuration + 's' : '获取验证码' }}
          </button>
        </template>
      </uni-easyinput>
    </view>
    <view class="input-container">
      <uni-easyinput
        v-model="loginForm.newPassword"
        :input-border="false"
        :clearable="false"
        type="password"
        placeholder="请输入新密码"
      ></uni-easyinput>
    </view>
    <button type="primary" class="confirm-btn" @click="resetPassword">提交</button>
  </view>
</template>
<script setup>
  import { reactive, ref } from 'vue';
  import dayjs from 'dayjs';
  import { authApi } from '@/apis/auth';
  import { useUserStore } from '@/store/modules/user';

  const userStore = useUserStore();
  const telCodeExpireDate = ref(dayjs(new Date()));
  const telCodeDuration = ref(0);
  const loginForm = reactive({
    tel: '',
    verifyCode: '',
    newPassword: ''
  });
  function bindCodeChange(val) {
    loginForm.verifyCode = val?.length > 6 ? val.substring(0, 6) : val;
  }
  function getCode(e) {
    if (!loginForm.tel) {
      uni.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }
    uni.showLoading({
      title: '发送中...'
    });
    authApi.sendSmsCode({
      phone: loginForm.tel,
      codeType: 'reset',
    })
      .then(() => {
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        });
        telCodeDuration.value = 60;
        telCodeExpireDate.value = dayjs().add(60, 'second');
        const timer = setInterval(() => {
          if (telCodeDuration.value > 0) {
            telCodeDuration.value = telCodeExpireDate.value.diff(dayjs(new Date()), 's');
          } else {
            clearInterval(timer);
          }
        }, 1000);
      })
      .finally(() => {
        uni.hideLoading();
      });
  }

  function resetPassword() {
    if (!loginForm.tel) {
      uni.showToast({
        title: '请输入手机号',
        icon: 'none'
      });
      return;
    }
    if (!loginForm.verifyCode) {
      uni.showToast({
        title: '请输入验证码',
        icon: 'none'
      });
      return;
    }
    uni.showLoading({
      title: '重置中...'
    });
    authApi.resetPassword({ ...loginForm })
      .then(() => {
        uni.showModal({
          title: '提示',
          content: '密码重置成功，请您重新登录',
          showCancel: false,
          success: () => {
            userStore.logout();
          }
        });
      })
      .finally(() => {
        uni.hideLoading();
      });
  }
</script>
<style lang="scss" scoped>
  .rebind-tel-page {
    padding: $uni-spacing-col-lg $uni-spacing-row-lg;
    &__title {
      font-size: 16px;
      font-weight: bold;
      padding-left: 10px;
    }
    .input-container {
      border-bottom: 1px solid whitesmoke;
      margin-top: $uni-spacing-col-lg;
    }
    .confirm-btn {
      margin-top: $uni-spacing-col-lg * 3;
      border-radius: 64px;
    }
  }
</style>
