/**
 * 认证服务
 * 处理登录、登出、token刷新等认证相关操作
 * 避免循环依赖问题
 */
import { authApi } from '@/apis/auth'

class AuthService {
  constructor() {
    this.userStore = null
  }

  // 设置用户store引用
  setUserStore(store) {
    this.userStore = store
  }

  // 登录
  async login(loginData) {
    // 只处理API调用，不操作store
    return await authApi.login(loginData)
  }

  // 登出
  async logout() {
    // 只处理API调用，不操作store
    return await authApi.logout()
  }

  // 刷新token
  async refreshToken() {
    if (!this.userStore) {
      throw new Error('User store not initialized')
    }

    const refreshToken = this.userStore.refreshToken?.value || ''
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await authApi.refreshToken(refreshToken)

      // 根据实际API响应结构调整
      const { accessToken, refreshToken: newRefreshToken, expiresIn = 7200 } = response

      // 直接设置token值，避免调用方法
      this.userStore.accessToken.value = accessToken
      this.userStore.refreshToken.value = newRefreshToken
      const finalExpireTime = typeof expiresIn === 'number' && expiresIn < Date.now()
        ? Date.now() + (expiresIn * 1000) - (5 * 60 * 1000)
        : expiresIn
      this.userStore.tokenExpireTime.value = finalExpireTime

      // 同步到本地存储
      uni.setStorageSync('access_token', accessToken)
      uni.setStorageSync('refresh_token', newRefreshToken)
      uni.setStorageSync('token_expire_time', finalExpireTime)

      return accessToken
    } catch (error) {
      // 直接清除token值
      this.userStore.accessToken.value = ''
      this.userStore.refreshToken.value = ''
      this.userStore.tokenExpireTime.value = 0

      // 清除本地存储
      uni.removeStorageSync('access_token')
      uni.removeStorageSync('refresh_token')
      uni.removeStorageSync('token_expire_time')

      throw error
    }
  }

  // 处理认证错误
  handleAuthError() {
    if (this.userStore) {
      // 直接清除token值
      this.userStore.accessToken.value = ''
      this.userStore.refreshToken.value = ''
      this.userStore.tokenExpireTime.value = 0

      // 清除本地存储
      uni.removeStorageSync('access_token')
      uni.removeStorageSync('refresh_token')
      uni.removeStorageSync('token_expire_time')
    }

    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    })

    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }, 1500)
  }

  // 获取访问令牌
  getAccessToken() {
    return this.userStore?.accessToken?.value || ''
  }

  // 获取刷新令牌
  getRefreshToken() {
    return this.userStore?.refreshToken?.value || ''
  }

  // 检查是否有有效token
  hasValidToken() {
    return this.userStore?.hasValidToken?.value || false
  }
}

// 创建单例实例
const authService = new AuthService()

export default authService
export { AuthService }
