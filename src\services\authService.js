/**
 * 认证服务
 * 处理登录、登出、token刷新等认证相关操作
 * 避免循环依赖问题
 */

class AuthService {
  constructor() {
    this.userStore = null
  }

  // 设置用户store引用
  setUserStore(store) {
    this.userStore = store
  }





  // 处理认证错误
  handleAuthError() {
    if (this.userStore) {
      // 直接清除token值
      this.userStore.accessToken.value = ''
      this.userStore.refreshToken.value = ''
      this.userStore.tokenExpireTime.value = 0

      // 清除本地存储
      uni.removeStorageSync('access_token')
      uni.removeStorageSync('refresh_token')
      uni.removeStorageSync('token_expire_time')
    }

    uni.showToast({
      title: '登录已过期，请重新登录',
      icon: 'none'
    })

    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }, 1500)
  }

  // 获取访问令牌
  getAccessToken() {
    return this.userStore?.accessToken?.value || ''
  }

  // 获取刷新令牌
  getRefreshToken() {
    return this.userStore?.refreshToken?.value || ''
  }

  // 检查是否有有效token
  hasValidToken() {
    return this.userStore?.hasValidToken?.value || false
  }
}

// 创建单例实例
const authService = new AuthService()

export default authService
export { AuthService }
