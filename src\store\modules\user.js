import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import authService from '@/services/authService'
import 
export const useUserStore = defineStore('user', () => {
  // 本地存储键名
  const ACCESS_TOKEN_KEY = 'access_token'
  const REFRESH_TOKEN_KEY = 'refresh_token'
  const TOKEN_EXPIRE_KEY = 'token_expire_time'

  // 状态
  const userInfo = ref(null)
  const isLoggedIn = ref(false)

  // Token相关状态
  const accessToken = ref('')
  const refreshToken = ref('')
  const accessTokenExpireTime = ref(0)
  const refreshTokenExpireTime = ref(0)

  // 计算属性
  const hasValidToken = computed(() => {
    return accessToken.value && tokenExpireTime.value > Date.now()
  })

  const isTokenExpired = computed(() => {
    return tokenExpireTime.value <= Date.now()
  })

  const tokenRemainingTime = computed(() => {
    const remainingMs = tokenExpireTime.value - Date.now()
    return Math.max(0, Math.floor(remainingMs / 1000))
  })

  const isTokenExpiringSoon = computed(() => {
    const remainingTime = tokenRemainingTime.value
    return remainingTime > 0 && remainingTime <= 300 // 5分钟
  })

  // Token管理方法
  const setTokens = (newAccessToken, newRefreshToken,accessTokenExpiresIn, refreshTokenExpiresIn) => {
    // 如果expireTime是秒数，需要转换为时间戳
    accessToken.value = newAccessToken
    refreshToken.value = newRefreshToken
    tokenExpireTime.value = finalExpireTime

    // 同步到本地存储
    uni.setStorageSync(ACCESS_TOKEN_KEY, newAccessToken)
    uni.setStorageSync(REFRESH_TOKEN_KEY, newRefreshToken)
    uni.setStorageSync(TOKEN_EXPIRE_KEY, finalExpireTime)
  }

  const clearTokens = () => {
    accessToken.value = ''
    refreshToken.value = ''
    tokenExpireTime.value = 0

    // 清除本地存储
    uni.removeStorageSync(ACCESS_TOKEN_KEY)
    uni.removeStorageSync(REFRESH_TOKEN_KEY)
    uni.removeStorageSync(TOKEN_EXPIRE_KEY)
  }

  // 从本地存储恢复token
  const restoreTokensFromStorage = () => {
    const storedAccessToken = uni.getStorageSync(ACCESS_TOKEN_KEY)
    const storedRefreshToken = uni.getStorageSync(REFRESH_TOKEN_KEY)
    const storedExpireTime = uni.getStorageSync(TOKEN_EXPIRE_KEY)

    if (storedAccessToken && storedRefreshToken) {
      accessToken.value = storedAccessToken
      refreshToken.value = storedRefreshToken
      tokenExpireTime.value = storedExpireTime || 0
    }
  }

  // 验证token格式
  const validateTokenFormat = (token) => {
    if (!token || typeof token !== 'string') {
      return false
    }
    // 简单的JWT格式验证（三个部分用.分隔）
    const parts = token.split('.')
    return parts.length === 3
  }

  // 动作
  const login = async (loginData) => {
    try {
      const response = await authService.login(loginData)

      // 在store中直接处理token设置，避免循环调用
      const { accessToken, refreshToken, accessTokenExpiresIn = 7200 , refreshTokenExpiresIn} = response
      setTokens(accessToken, refreshToken, accessTokenExpiresIn,refreshTokenExpiresIn)

      // 设置用户信息
      if (response.userInfo) {
        userInfo.value = response.userInfo
        uni.setStorageSync('userInfo', response.userInfo)
      }

      isLoggedIn.value = true

      return response
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      await authService.logout()
    } catch (error) {
      console.warn('Logout API failed:', error)
    } finally {
      // 在store中直接处理状态清除
      userInfo.value = null
      isLoggedIn.value = false
      clearTokens()

      // 清除本地存储
      uni.removeStorageSync('userInfo')

      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }
  }

  const updateUserInfo = (newUserInfo) => {
    userInfo.value = { ...userInfo.value, ...newUserInfo }
    uni.setStorageSync('userInfo', userInfo.value)
  }

  const initUserState = () => {
    // 设置authService的store引用
    authService.setUserStore({
      userInfo,
      isLoggedIn,
      accessToken,
      refreshToken,
      tokenExpireTime,
      hasValidToken,
      setTokens,
      clearTokens
    })

    // 从本地存储恢复token
    restoreTokensFromStorage()

    // 从本地存储恢复用户信息
    const storedUserInfo = uni.getStorageSync('userInfo')
    if (storedUserInfo) {
      userInfo.value = storedUserInfo
    }

    // 检查token状态
    isLoggedIn.value = hasValidToken.value

    // 如果有用户信息但token无效，清除用户信息
    if (userInfo.value && !hasValidToken.value) {
      userInfo.value = null
      uni.removeStorageSync('userInfo')
    }
  }

  const checkAuthStatus = () => {
    const hasToken = hasValidToken.value

    if (!hasToken && isLoggedIn.value) {
      // token失效，清除登录状态
      logout()
    }

    return hasToken
  }

  return {
    // 状态
    userInfo,
    isLoggedIn,
    accessToken,
    refreshToken,
    tokenExpireTime,

    // 计算属性
    hasValidToken,
    isTokenExpired,
    tokenRemainingTime,
    isTokenExpiringSoon,

    // 动作
    login,
    logout,
    updateUserInfo,
    initUserState,
    checkAuthStatus,

    // Token管理方法
    setTokens,
    clearTokens,
    restoreTokensFromStorage,
    validateTokenFormat
  }
}, {
  // 使用 pinia-plugin-unistorage 持久化部分状态
  unistorage: {
    paths: ['userInfo', 'isLoggedIn', 'accessToken', 'refreshToken', 'tokenExpireTime']
  }
})