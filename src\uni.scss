/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
@import "uview-plus/theme.scss";
/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #1bb2b2;
$uni-color-second-primary: #beeded;
$uni-color-success: #1bb2b2;
$uni-color-warning: #ff884c;
$uni-color-error: #ff5967;
$uni-color-disabled: #adbcbf;

/* 标题基本色 */
$uni-title-color: #262626;

/* 文字基本颜色 */
$uni-text-color: #404040; // 基本色
$uni-text-color-inverse: #fff; // 反色
$uni-text-color-grey: #727d7f; // 辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable: #adbcbf;

/* 背景颜色 */
$uni-bg-color: #fff;
$uni-bg-color-grey: #ebf0f2;
$uni-bg-color-hover: #f1f1f1; // 点击状态颜色
$uni-bg-color-mask: rgba(0, 0, 0, 0.65); // 遮罩颜色

/* 边框颜色 */
$uni-border-color: #a1b5b5;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 图片尺寸 */
$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

/* Border Radius */
$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 6rpx;
$uni-border-radius-lg: 8rpx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10rpx;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 垂直间距 */
$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2c405a; // 文章标题颜色
$uni-font-size-title: 20px;
$uni-color-subtitle: #555; // 二级标题颜色
$uni-font-size-subtitle: 18px;
$uni-color-paragraph: #3f536e; // 文章段落颜色
$uni-font-size-paragraph: 15px;

$uni-full-height: calc(100vh - 44px);
$uni-full-height-no-top-bar: 100vh;

// uni-ui自定义主题
// 间距基础倍数
$uni-space-root: 2;
// 边框半径默认值
$uni-radius-root: 5px;

// 主色
$uni-primary: #1bb2b2;
$uni-primary-disable: mix(#fff, $uni-primary, 50%);
$uni-primary-light: mix(#197780, $uni-primary, 80%);

// 辅助色
// 除了主色外的场景色，需要在不同的场景中使用（例如危险色表示危险的操作）。
$uni-success: #1bb2b2;
$uni-success-disable: mix(#fff, $uni-success, 50%);
$uni-success-light: mix(#fff, $uni-success, 80%);

$uni-warning: #ff884c;
$uni-warning-disable: mix(#fff, $uni-warning, 50%);
$uni-warning-light: mix(#fff, $uni-warning, 80%);

$uni-error: #ff5967;
$uni-error-disable: mix(#fff, $uni-error, 50%);
$uni-error-light: mix(#fff, $uni-error, 80%);

$uni-info: #8f939c;
$uni-info-disable: mix(#fff, $uni-info, 50%);
$uni-info-light: mix(#fff, $uni-info, 80%);

// 中性色
// 中性色用于文本、背景和边框颜色。通过运用不同的中性色，来表现层次结构。
$uni-main-color: #3a3a3a; // 主要文字
$uni-base-color: #6a6a6a; // 常规文字
$uni-secondary-color: #909399; // 次要文字
$uni-extra-color: #c7c7c7; // 辅助说明

// 边框颜色
$uni-border-1: #f0f0f0;
$uni-border-2: #ebf0f2;
$uni-border-3: #dae2e5;
$uni-border-4: #b9b9b9;
$uni-border-5: #e6e6e6;
// 常规色
$uni-black: #000000;
$uni-white: #ffffff;
$uni-transparent: rgba(
  $color: #000000,
  $alpha: 0,
);

/* 水平间距 */
$uni-spacing-sm: 16rpx;
$uni-spacing-base: 30rpx;
$uni-spacing-lg: 60rpx;

// 阴影
$uni-shadow-sm: 0 0 10rpx
  rgba(
    $color: #d8d8d8,
    $alpha: 0.5,
  );
$uni-shadow-base: 0 2rpx 16rpx 2rpx
  rgba(
    $color: #a5a5a5,
    $alpha: 0.2,
  );
$uni-shadow-lg: 0px 2rpx 20rpx 4rpx
  rgba(
    $color: #a5a4a4,
    $alpha: 0.5,
  );

// 蒙版
$uni-mask: rgba(
  $color: #000000,
  $alpha: 0.4,
);

// #ifdef H5
// vite dev环境样式动态加载有问题，具体见https://github.com/vitejs/vite/issues/3924，为避免测试环境中每个文件夹注入样式重置文件，在本处注入
@import '@/assets/style/reset/index.scss';
// #endif
