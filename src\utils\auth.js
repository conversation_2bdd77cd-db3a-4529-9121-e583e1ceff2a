/**
 * 认证相关工具函数
 * 负责初始化认证系统和处理认证状态
 */
import request from '@/utils/request/uni-request'
import authService from '@/services/authService'

/**
 * 初始化认证系统
 * 需要在应用启动时调用
 * @param {Object} userStore - 用户store实例
 */
export function initAuth(userStore) {
  // 设置request的store引用
  request.setUserStore(userStore)

  // 初始化用户状态
  userStore.initUserState()

  console.log('认证系统初始化完成')
}

/**
 * 检查用户是否需要登录
 * @returns {boolean} 是否需要登录
 */
export function needLogin() {
  return !authService.hasValidToken()
}

/**
 * 跳转到登录页面
 * @param {string} redirectUrl - 登录成功后的重定向地址
 */
export function redirectToLogin(redirectUrl = '') {
  const url = redirectUrl ? `/pages/login/login?redirect=${encodeURIComponent(redirectUrl)}` : '/pages/login/login'
  uni.reLaunch({ url })
}

/**
 * 检查页面访问权限
 * 如果未登录则跳转到登录页
 * @param {string} currentPath - 当前页面路径
 * @returns {boolean} 是否有访问权限
 */
export function checkPageAuth(currentPath = '') {
  if (needLogin()) {
    redirectToLogin(currentPath)
    return false
  }
  return true
}

/**
 * 获取token剩余时间的友好显示
 * @returns {string} 剩余时间描述
 */
export function getTokenRemainingTimeText() {
  // 通过authService获取userStore的tokenRemainingTime
  const userStore = authService.userStore
  const remainingSeconds = userStore?.tokenRemainingTime?.value || 0

  if (remainingSeconds <= 0) {
    return 'Token已过期'
  }

  const hours = Math.floor(remainingSeconds / 3600)
  const minutes = Math.floor((remainingSeconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟`
  } else {
    return `${remainingSeconds}秒`
  }
}

/**
 * 监听token状态变化
 * @param {Function} callback - 状态变化回调函数
 */
export function watchTokenStatus(callback) {
  let lastStatus = authService.hasValidToken()

  const checkStatus = () => {
    const currentStatus = authService.hasValidToken()
    if (currentStatus !== lastStatus) {
      lastStatus = currentStatus
      callback(currentStatus)
    }
  }

  // 每30秒检查一次token状态
  const timer = setInterval(checkStatus, 30000)

  // 返回清理函数
  return () => clearInterval(timer)
}

export default {
  initAuth,
  needLogin,
  redirectToLogin,
  checkPageAuth,
  getTokenRemainingTimeText,
  watchTokenStatus
}
