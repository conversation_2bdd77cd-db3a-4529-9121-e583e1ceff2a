import axios from 'axios'
import mpAdapter from 'uniapp-axios-adapter'

const BASE_API = import.meta.env.VITE_API_ADDRESS

/**
 * 专门用于认证相关API的HTTP客户端
 * 避免与主请求客户端形成循环依赖
 */
class AuthRequest {
  constructor() {
    this.baseUrl = BASE_API
    this.setupAxios()
  }

  // 配置axios
  setupAxios() {
    // 配置适配器
    axios.defaults.adapter = mpAdapter

    // 创建独立的axios实例，专门用于认证
    this.instance = axios.create({
      baseURL: this.baseUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // 简单的请求拦截器（不添加token，因为这是认证请求）
    this.instance.interceptors.request.use(
      (config) => {
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 简单的响应拦截器（不处理401，因为这是认证请求）
    this.instance.interceptors.response.use(
      (response) => {
        return response
      },
      (error) => {
        return Promise.reject(error)
      }
    )
  }

  // 处理参数，移除空值
  handleParams(params) {
    if (!params) return {}
    const result = {}
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        result[key] = params[key]
      }
    })
    return result
  }

  // 处理URL
  handleUrl(path) {
    const Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\\/?%&=]*)?/;
    const objExp = new RegExp(Expression);
    if (objExp.test(path)) {
      return path
    }
    return path.startsWith('/') ? path : `/${path}`
  }

  // 核心请求方法
  async request(options) {
    const {
      url,
      method = 'GET',
      data,
      params,
      headers = {}
    } = options

    try {
      // 处理请求参数
      const requestParams = this.handleParams(params)
      const requestData = this.handleParams(data)

      // 构建请求配置
      const config = {
        url: this.handleUrl(url),
        method: method.toLowerCase(),
        headers: { ...headers }
      }

      // 根据请求方法添加数据
      if (['get', 'delete'].includes(config.method)) {
        config.params = requestParams
      } else {
        config.data = requestData
        // 如果POST请求有params，也要添加到config中
        if (requestParams && Object.keys(requestParams).length > 0) {
          config.params = requestParams
        }
      }

      const response = await this.instance(config)
      
      // 处理响应数据
      const result = response.data
      if (result.code === 200 || result.success) {
        return result.data || result
      } else {
        throw new Error(result.message || '请求失败')
      }
    } catch (error) {
      // 网络错误处理
      if (!error.response) {
        uni.showToast({
          title: '请检查当前网络环境',
          icon: 'none'
        })
      }
      throw error
    }
  }

  // 对外暴露的方法
  get(url, params = {}, options = {}) {
    return this.request({
      url,
      method: 'GET',
      params,
      ...options
    })
  }

  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    })
  }
}

const authRequest = new AuthRequest()

// 导出认证专用请求实例
export default authRequest
