import axios from 'axios'
import mpAdapter from 'uniapp-axios-adapter'
import authService from '@/services/authService'

const BASE_API = import.meta.env.VITE_API_ADDRESS

// 全局变量用于管理刷新状态
let isRefreshing = false
let failedQueue = []
let requestInstance = null // 保存axios实例的引用

// 处理队列中的请求
function processQueue(error) {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve()
    }
  })
  failedQueue = []
}

// 刷新token - 直接调用API避免循环依赖
async function refreshToken() {
  const userStore = authService.userStore
  if (!userStore) {
    throw new Error('User store not initialized')
  }

  const refreshTokenValue = userStore.refreshToken?.value || ''
  if (!refreshTokenValue) {
    throw new Error('No refresh token available')
  }

  try {
    // 直接使用axios而不是request实例，避免循环依赖
    const response = await axios.post(`${BASE_API}/public/auth/token/refresh`, {
      refreshToken: refreshTokenValue
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      adapter: mpAdapter
    })

    const result = response.data
    let data
    if (result.code === 200 || result.success) {
      data = result.data || result
    } else {
      throw new Error(result.message || '刷新token失败')
    }

    const { accessToken, refreshToken: newRefreshToken, expiresIn = 7200 } = data

    // 直接设置token值，避免调用方法
    userStore.accessToken.value = accessToken
    userStore.refreshToken.value = newRefreshToken
    const finalExpireTime = typeof expiresIn === 'number' && expiresIn < Date.now()
      ? Date.now() + (expiresIn * 1000) - (5 * 60 * 1000)
      : expiresIn
    userStore.tokenExpireTime.value = finalExpireTime

    // 同步到本地存储
    uni.setStorageSync('access_token', accessToken)
    uni.setStorageSync('refresh_token', newRefreshToken)
    uni.setStorageSync('token_expire_time', finalExpireTime)

    return accessToken
  } catch (error) {
    // 直接清除token值
    userStore.accessToken.value = ''
    userStore.refreshToken.value = ''
    userStore.tokenExpireTime.value = 0

    // 清除本地存储
    uni.removeStorageSync('access_token')
    uni.removeStorageSync('refresh_token')
    uni.removeStorageSync('token_expire_time')

    throw error
  }
}

// 处理认证错误
function handleAuthError() {
  authService.handleAuthError()
}

class Request {
  constructor() {
    this.baseUrl = BASE_API
    this.userStore = null // 将在初始化时设置
    this.setupAxios()
  }

  // 设置用户store引用
  setUserStore(store) {
    this.userStore = store
  }

  // 配置axios
  setupAxios() {
    // 配置适配器
    axios.defaults.adapter = mpAdapter

    // 创建axios实例
    this.instance = axios.create({
      baseURL: this.baseUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // 保存实例引用到全局变量
    requestInstance = this.instance

    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        const accessToken = authService.getAccessToken()
        if (accessToken && config.headers) {
          config.headers.Authorization = `Bearer ${accessToken}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response) => {
        return response
      },
      async (error) => {
        const originalRequest = error.config

        // 处理401错误（token过期）
        if (error.response?.status === 401 && !originalRequest._retry) {
          if (isRefreshing) {
            // 如果正在刷新token，将请求加入队列
            return new Promise((resolve, reject) => {
              failedQueue.push({ resolve, reject })
            }).then(() => {
              // 使用全局实例引用，避免this引用导致的循环调用
              return requestInstance(originalRequest)
            })
          }

          originalRequest._retry = true
          isRefreshing = true

          try {
            await refreshToken()
            processQueue(null)
            // 使用全局实例引用，避免this引用导致的循环调用
            return requestInstance(originalRequest)
          } catch (refreshError) {
            processQueue(refreshError)
            handleAuthError()
            return Promise.reject(refreshError)
          } finally {
            isRefreshing = false
          }
        }

        return Promise.reject(error)
      }
    )
  }



  // 处理参数，移除空值
  handleParams(params) {
    if (!params) return {}
    const result = {}
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        result[key] = params[key]
      }
    })
    return result
  }

  // 获取token（保持向后兼容）
  getToken() {
    return authService.getAccessToken()
  }

  // 处理URL
  handleUrl(path) {
    const Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\\/?%&=]*)?/;
    const objExp = new RegExp(Expression);
    if (objExp.test(path)) {
      return path
    }
    return path.startsWith('/') ? path : `/${path}`
  }

  // 核心请求方法
  async request(options) {
    const {
      url,
      method = 'GET',
      data,
      params,
      headers = {},
      isNeedToken = true
    } = options
    
    try {
      // 处理请求参数
      const requestParams = this.handleParams(params)
      const requestData = this.handleParams(data)

      // 构建请求配置
      const config = {
        url: this.handleUrl(url),
        method: method.toLowerCase(),
        headers: { ...headers }
      }

      // 根据请求方法添加数据
      if (['get', 'delete'].includes(config.method)) {
        config.params = requestParams
      } else {
        config.data = requestData
      }

      // 如果不需要token，临时移除Authorization头
      if (!isNeedToken && config.headers) {
        delete config.headers.Authorization
      }
      console.log('config', config);
      console.log('this.instance', this.instance);
      
      const response = await this.instance(config)
      
      // 处理响应数据
      const result = response.data
      if (result.code === 200 || result.success) {
        return result.data || result
      } else {
        throw new Error(result.message || '请求失败')
      }
    } catch (error) {
      // 网络错误处理
      if (!error.response) {
        uni.showToast({
          title: '请检查当前网络环境',
          icon: 'none'
        })
      }
      throw error
    }
  }



  // 对外暴露的方法
  get(url, params = {}, options = {}) {
    return this.request({
      url,
      method: 'GET',
      params,
      ...options
    })
  }

  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    })
  }

  // 获取认证服务实例
  getAuthService() {
    return authService
  }
}

const request = new Request()

// 导出请求实例
export default request
