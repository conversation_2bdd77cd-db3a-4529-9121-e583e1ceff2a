import axios from 'axios'
import {UniAdapter } from 'uniapp-axios-adapter'
import authService from '@/services/authService'
import { secondsToTimestamp } from '@/utils/utils'
const BASE_API = import.meta.env.VITE_API_ADDRESS

// 全局变量用于管理刷新状态
let isRefreshing = false
let failedQueue = []

// 处理队列中的请求
function processQueue(error) {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve()
    }
  })
  failedQueue = []
}

// 创建全局axios实例
const createAxiosInstance = () => {
  // 配置适配器

  // 创建axios实例
  const instance = axios.create({
    baseURL: BASE_API,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json'
    },
    adapter:UniAdapter
  })
  return instance
}

// 创建全局实例
const axiosInstance = createAxiosInstance()
// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    const accessToken = authService.getAccessToken()
    if (accessToken && config.headers && config.headers.isNeedToken ) {
      config.headers.Authorization = `Bearer ${accessToken}`
    }
    delete config.headers.isNeedToken
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    // 处理401错误（token过期）
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // 如果正在刷新token，将请求加入队列
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject })
        }).then(() => {
          return axiosInstance(originalRequest)
        })
      }

      originalRequest._retry = true
      isRefreshing = true

      try {
        await refreshToken()
        processQueue(null)
        return axiosInstance(originalRequest)
      } catch (refreshError) {
        processQueue(refreshError)
        handleAuthError()
        return Promise.reject(refreshError)
      } finally {
        isRefreshing = false
      }
    }

    return Promise.reject(error)
  }
)

// 刷新token - 直接调用API避免循环依赖
async function refreshToken() {
  const userStore = authService.userStore
  if (!userStore) {
    throw new Error('User store not initialized')
  }

  const refreshTokenValue = userStore.refreshToken?.value || ''
  if (!refreshTokenValue) {
    throw new Error('No refresh token available')
  }

  // 检查 refreshToken 是否过期
  if (userStore.refreshTokenExpireTime?.value && userStore.refreshTokenExpireTime.value <= Date.now()) {
    throw new Error('Refresh token expired')
  }

  try {
    // 直接使用axios而不是request实例，避免循环依赖
    const response = await axios.post(`${BASE_API}/public/auth/token/refresh`, {
      refreshToken: refreshTokenValue
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      adapter: UniAdapter
    })

    const result = response.data
    let data
    if (result.code === 200 || result.success) {
      data = result.data || result
    } else {
      throw new Error(result.message || '刷新token失败')
    }

    const {
      accessToken,
      refreshToken: newRefreshToken,
      accessTokenExpiresIn = 7200,
      refreshTokenExpiresIn = 86400
    } = data

    // 计算过期时间
    const accessExpireTime = secondsToTimestamp(accessTokenExpiresIn)
    const refreshExpireTime = secondsToTimestamp(refreshTokenExpiresIn)

    // 直接设置token值，避免调用方法
    userStore.accessToken.value = accessToken
    userStore.refreshToken.value = newRefreshToken
    userStore.accessTokenExpireTime.value = accessExpireTime
    userStore.refreshTokenExpireTime.value = refreshExpireTime

    // 同步到本地存储
    uni.setStorageSync('access_token', accessToken)
    uni.setStorageSync('refresh_token', newRefreshToken)
    uni.setStorageSync('access_token_expire_time', accessExpireTime)
    uni.setStorageSync('refresh_token_expire_time', refreshExpireTime)

    return accessToken
  } catch (error) {
    // 直接清除token值
    userStore.accessToken.value = ''
    userStore.refreshToken.value = ''
    userStore.accessTokenExpireTime.value = 0
    userStore.refreshTokenExpireTime.value = 0

    // 清除本地存储
    uni.removeStorageSync('access_token')
    uni.removeStorageSync('refresh_token')
    uni.removeStorageSync('access_token_expire_time')
    uni.removeStorageSync('refresh_token_expire_time')

    throw error
  }
}

// 处理认证错误
function handleAuthError() {
  authService.handleAuthError()
}

class Request {
  constructor() {
    this.userStore = null // 将在初始化时设置
  }

  // 设置用户store引用
  setUserStore(store) {
    this.userStore = store
  }

  // 处理参数，移除空值
  handleParams(params) {
    if (!params) return {}
    const result = {}
    Object.keys(params).forEach(key => {
      if (params[key] !== null && params[key] !== undefined && params[key] !== '') {
        result[key] = params[key]
      }
    })
    return result
  }



  // 处理URL
  handleUrl(path) {
    const Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\\/?%&=]*)?/;
    const objExp = new RegExp(Expression);
    if (objExp.test(path)) {
      return path
    }
    return path.startsWith('/') ? path : `/${path}`
  }

  // 核心请求方法
  async request(options) {
    const {
      url,
      method = 'GET',
      data,
      params,
      headers = {},
      isNeedToken = true
    } = options
    
    try {
      // 处理请求参数
      const requestParams = this.handleParams(params)
      const requestData = this.handleParams(data)

      // 构建请求配置
      const config = {
        url: this.handleUrl(url),
        method: method.toLowerCase(),
        headers: { ...headers,isNeedToken }
      }

      // 根据请求方法添加数据
      if (['get', 'delete'].includes(config.method)) {
        config.params = requestParams
      } else {
        config.data = requestData
      }

      console.log('config', config);
      console.log('axiosInstance',axiosInstance.request);
      
      const response = await axiosInstance.request(config)
      
      // 处理响应数据
      const result = response.data
      if (result.code === 200 || result.success) {
        return result.data || result
      } else {
        throw new Error(result.message || '请求失败')
      }
    } catch (error) {
      // 网络错误处理
      if (!error.response) {
        uni.showToast({
          title: '请检查当前网络环境',
          icon: 'none'
        })
      }
      throw error
    }
  }



  // 对外暴露的方法
  get(url, params = {}, options = {}) {
    return this.request({
      url,
      method: 'GET',
      params,
      ...options
    })
  }

  post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    })
  }

  put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    })
  }

  delete(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      data,
      ...options
    })
  }

  // 获取认证服务实例
  getAuthService() {
    return authService
  }
}

const request = new Request()

// 导出请求实例
export default request
