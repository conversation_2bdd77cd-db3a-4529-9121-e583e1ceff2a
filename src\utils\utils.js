/*
 * @Author: limingshuang <EMAIL>
 * @Date: 2025-07-15 18:00:16
 * @LastEditors: limingshuang <EMAIL>
 * @LastEditTime: 2025-07-15 18:18:14
 * @FilePath: \escort_service\src\utils\utils.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */


/**
 * @description: 通过Object构建params
 * @return {*}
 */
export function combineQueryParam(params) {
  const fix = '';
  if (typeof params !== 'object' || params instanceof Array) {
    return fix;
  }
  const keys = Object.keys(params);
  if (keys.length === 0) {
    return fix;
  }
  const result = keys
    .map((key) => {
      let value = '';
      if (Array.isArray(params[key])) {
        value = params[key].map((i) => encodeURIComponent(i)).join(',');
      } else {
        value = encodeURIComponent(params[key]);
      }
      return key + '=' + value;
    })
    .join('&');
  if (fix.length > 0) {
    return [result, fix].join('&');
  }
  return result;
}

/**
 * @description: 通过url和param构建 完整url
 * @return {*}
 */
export function combineFinalUrl(url, params) {
  const paramStr = combineQueryParam(params);
  if (paramStr.length === 0) {
    return url;
  }
  return url + '?' + paramStr;
}